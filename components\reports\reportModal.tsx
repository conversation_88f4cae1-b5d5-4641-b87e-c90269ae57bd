import React, { useEffect, useState } from "react";
import {
  Autocomplete,
  AutocompleteItem,
  Button,
  Checkbox,
  Modal,
  ModalBody,
  Modal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Spinner,
  useDisclosure,
} from "@heroui/react";
import { Icon } from "@iconify/react";

import { useReportsList } from "@/hooks/reports/useReportsList";
import { useReportGenerate } from "@/hooks/reports/useReportGenerate";
import { ApiReport } from "@/types/report";

export interface ReportModalProps {
  isOpen: boolean;
  onClose: () => void;
  projectId: string | number;
}

export const ReportModal: React.FC<ReportModalProps> = ({
  isOpen,
  onClose,
  projectId,
}) => {
  const { reports, fetchActiveReports } = useReportsList();
  const {
    generateProjectPlan,
    generateReport,
    isGenerating,
    error: generateError,
  } = useReportGenerate(projectId);
  const [selectedReport, setSelectedReport] = useState<ApiReport | null>(null);
  const [english, setEnglish] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const {
    isOpen: isSuccessOpen,
    onOpen: onSuccessOpen,
    onClose: onSuccessClose,
  } = useDisclosure();
  const {
    isOpen: isErrorOpen,
    onOpen: onErrorOpen,
    onClose: onErrorClose,
  } = useDisclosure();

  useEffect(() => {
    fetchActiveReports();
  }, []);

  const handleReportSelect = (reportId: React.Key | null) => {
    if (reportId === null) {
      setSelectedReport(null);

      return;
    }

    let report = reports.find((r) => r.id == reportId);

    if (reportId == -1) {
      report = {
        id: Number(reportId),
        name: "Project Plan",
        description: "Reporte personalizado del plan de proyecto",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        projectId: projectId.toString(),
        document_name: "Project Plan",
        observations: false,
        use_english: false,
        filters: {},
        is_active: true,
        project_id: projectId.toString(),
        active: true,
        created_at: new Date().toISOString(),
      };
    }

    setSelectedReport(report || null);
  };

  const handleDownload = async () => {
    if (!selectedReport) {
      return;
    }

    setIsDownloading(true);
    setErrorMessage(null);

    try {
      let response: { blob: Blob; headers: any };
      const language = english ? "en" : "es";

      // Special handling for Project Plan (id: -1)
      if (selectedReport.id === -1) {
        // Call the API to generate the project plan
        response = await generateProjectPlan(language);
      } else {
        // For other report types, use the generate report API
        response = await generateReport(selectedReport.id, language);
      }

      // Create and trigger download
      const url = window.URL.createObjectURL(response.blob);
      const a = document.createElement("a");

      console.log("RR! response", response);

      const getFilenameFromContentDisposition = (
        contentDisposition: string,
      ): string => {
        console.log("RR! 1 Content-Disposition:", contentDisposition);
        if (!contentDisposition) return `${selectedReport.name}.pdf`;

        // Match filename="..." or filename*=UTF-8''...
        const filenameMatch = contentDisposition.match(
          /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/,
        );

        console.log("RR! 2 Content-Disposition:", contentDisposition);

        if (filenameMatch && filenameMatch[1]) {
          // Remove quotes if present
          return filenameMatch[1].replace(/['"]/g, "");
        }

        return `${selectedReport.name}.pdf`;
      };

      const contentDisposition =
        response.headers["content-disposition"] ||
        response.headers["Content-Disposition"];
      const filename = getFilenameFromContentDisposition(contentDisposition);

      a.style.display = "none";
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();

      // Clean up
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      // Show success message
      onSuccessOpen();
    } catch (error: any) {
      setErrorMessage(error.message || "Failed to download report");
      onErrorOpen();
    } finally {
      setIsDownloading(false);
    }
  };

  return (
    <>
      <Modal
        classNames={{
          base: "bg-content1",
          header: "border-b border-divider",
          body: "py-6",
          footer: "border-t border-divider",
        }}
        isOpen={isOpen}
        scrollBehavior="inside"
        size="xl"
        onClose={onClose}
      >
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">
                <h2 className="text-2xl font-bold">Generar Reportes</h2>
                <p className="text-sm text-foreground-500">
                  Selecciona y descarga reportes del proyecto
                </p>
              </ModalHeader>
              <ModalBody>
                <div className="space-y-6">
                  <div>
                    <p className="text-foreground-600">
                      Elige un tipo de reporte de la lista para generar y
                      descargar.
                    </p>
                  </div>
                  <Autocomplete
                    className="max-w-xs"
                    defaultItems={[
                      { id: -1, name: "Project Plan" },
                      ...reports,
                    ]}
                    label="Seleccionar Tipo de Reporte"
                    placeholder="Elegir un reporte"
                    startContent={
                      selectedReport?.id === -1 ? (
                        <Icon
                          className="text-yellow-500"
                          icon="lucide:sparkles"
                        />
                      ) : selectedReport ? (
                        <Icon icon="lucide:file-text" />
                      ) : null
                    }
                    style={{
                      // Change text color based on selection
                      color: selectedReport
                        ? "inherit"
                        : "var(--foreground-500)",
                    }}
                    onSelectionChange={handleReportSelect}
                  >
                    {(item) => (
                      <AutocompleteItem key={item.id} textValue={item.name}>
                        {item.id === -1 ? (
                          <div className="flex items-center gap-2">
                            <Icon
                              className="text-yellow-500"
                              icon="lucide:sparkles"
                            />
                            <span className="font-bold rainbow-text">
                              Project Plan
                            </span>
                          </div>
                        ) : (
                          <div className="flex items-center gap-2">
                            <Icon icon="lucide:file-text" />
                            <span>{item.name}</span>
                          </div>
                        )}
                      </AutocompleteItem>
                    )}
                  </Autocomplete>
                  <br />
                  <Checkbox
                    onChange={(_checked) => {
                      setEnglish(!english);
                    }}
                  >
                    Generar en inglés{" "}
                    {english ? " (Activado)" : " (Desactivado)"}
                  </Checkbox>
                </div>
              </ModalBody>
              <ModalFooter>
                <Button color="danger" variant="light" onPress={onClose}>
                  Cancelar
                </Button>
                <Button
                  color="primary"
                  disabled={!selectedReport || isDownloading || isGenerating}
                  variant="solid"
                  onPress={handleDownload}
                >
                  {isDownloading || isGenerating ? (
                    <div className="flex items-center gap-2">
                      <Spinner size="sm" />
                      <span>Generando...</span>
                    </div>
                  ) : (
                    <>
                      <Icon className="mr-2" icon="lucide:download" />
                      Descargar Reporte
                    </>
                  )}
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>

      {/* Success Modal */}
      <Modal isOpen={isSuccessOpen} size="sm" onClose={onSuccessClose}>
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col items-center">
                <Icon
                  className="text-success text-4xl mb-2"
                  icon="lucide:check-circle"
                />
                <h3 className="text-xl font-semibold">Descarga Exitosa</h3>
              </ModalHeader>
              <ModalBody>
                <p className="text-center">
                  Tu reporte ha sido descargado exitosamente. Puedes encontrarlo
                  en tu carpeta de descargas.
                </p>
              </ModalBody>
              <ModalFooter>
                <Button color="primary" onPress={onClose}>
                  Cerrar
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>

      {/* Error Modal */}
      <Modal isOpen={isErrorOpen} size="sm" onClose={onErrorClose}>
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col items-center">
                <Icon
                  className="text-danger text-4xl mb-2"
                  icon="lucide:x-circle"
                />
                <h3 className="text-xl font-semibold">Error</h3>
              </ModalHeader>
              <ModalBody>
                <p className="text-center">
                  {errorMessage ||
                    "Ocurrió un error al generar el reporte. Por favor intenta nuevamente."}
                </p>
              </ModalBody>
              <ModalFooter>
                <Button color="primary" onPress={onClose}>
                  Cerrar
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
};
